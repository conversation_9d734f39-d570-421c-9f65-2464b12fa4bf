/**
 * UTC+12 Time Helper Utility
 * Provides consistent UTC+12 timestamp generation and date operations
 * All timestamps are created in UTC+12 timezone (Pacific/Fiji)
 */
export class UTCTimeHelper {
  // UTC+12 offset in milliseconds (12 hours * 60 minutes * 60 seconds * 1000 milliseconds)
  private static readonly UTC_PLUS_12_OFFSET = 12 * 60 * 60 * 1000;

  /**
   * Get current time in UTC+12 as Date object
   * @returns Current time in UTC+12 as Date object
   */
  static now(): Date {
    const utcNow = new Date();
    return new Date(utcNow.getTime() + this.UTC_PLUS_12_OFFSET);
  }

  /**
   * Get current time in UTC+12 as ISO string
   * @returns Current time in UTC+12 in ISO 8601 format
   */
  static nowISOString(): string {
    return this.now().toISOString();
  }

  /**
   * Get current time in UTC+12 as UTC string
   * @returns Current time in UTC+12 as UTC string
   */
  static nowUTCString(): string {
    return this.now().toUTCString();
  }

  /**
   * Convert any date to UTC+12
   * @param date - Date to convert to UTC+12
   * @returns Date adjusted to UTC+12
   */
  static toUTCPlus12(date: Date): Date {
    const utcTime = new Date(date.getTime() - (date.getTimezoneOffset() * 60 * 1000));
    return new Date(utcTime.getTime() + this.UTC_PLUS_12_OFFSET);
  }

  /**
   * Get start of day for given date (or current date) in UTC+12
   * @param date - Optional date to get start of day for (defaults to current UTC+12 time)
   * @returns Start of day as Date object in UTC+12
   */
  static getStartOfDay(date?: Date): Date {
    const d = date || this.now();
    const utc12Date = this.toUTCPlus12(d);
    return new Date(utc12Date.getFullYear(), utc12Date.getMonth(), utc12Date.getDate());
  }

  /**
   * Get start of week for given date (or current date) in UTC+12
   * @param date - Optional date to get start of week for (defaults to current UTC+12 time)
   * @returns Start of week as Date object in UTC+12
   */
  static getStartOfWeek(date?: Date): Date {
    const d = date || this.now();
    const startOfDay = this.getStartOfDay(d);
    const day = startOfDay.getDay();
    return new Date(startOfDay.setDate(startOfDay.getDate() - day));
  }

  /**
   * Get start of month for given date (or current date) in UTC+12
   * @param date - Optional date to get start of month for (defaults to current UTC+12 time)
   * @returns Start of month as Date object in UTC+12
   */
  static getStartOfMonth(date?: Date): Date {
    const d = date || this.now();
    const utc12Date = this.toUTCPlus12(d);
    return new Date(utc12Date.getFullYear(), utc12Date.getMonth(), 1);
  }

  /**
   * Add days to a date in UTC+12
   * @param date - Base date
   * @param days - Number of days to add
   * @returns New date with days added in UTC+12
   */
  static addDays(date: Date, days: number): Date {
    const utc12Date = this.toUTCPlus12(date);
    const result = new Date(utc12Date);
    result.setDate(result.getDate() + days);
    return result;
  }

  /**
   * Subtract days from a date in UTC+12
   * @param date - Base date
   * @param days - Number of days to subtract
   * @returns New date with days subtracted in UTC+12
   */
  static subtractDays(date: Date, days: number): Date {
    return this.addDays(date, -days);
  }

  /**
   * Check if a timestamp is a valid Date object or string and convert to UTC+12
   * @param timestamp - Timestamp to validate
   * @returns Valid Date object in UTC+12
   */
  static ensureDate(timestamp: Date | string | number): Date {
    let date: Date;
    if (timestamp instanceof Date) {
      date = timestamp;
    } else {
      date = new Date(timestamp);
    }
    return this.toUTCPlus12(date);
  }

  /**
   * Format a date in UTC+12 timezone for display
   * @param date - Date to format
   * @param options - Intl.DateTimeFormatOptions
   * @returns Formatted date string
   */
  static formatInUTCPlus12(date: Date, options?: Intl.DateTimeFormatOptions): string {
    const defaultOptions: Intl.DateTimeFormatOptions = {
      timeZone: 'Pacific/Fiji',
      ...options
    };
    return date.toLocaleString('en-US', defaultOptions);
  }

  /**
   * Get the current timezone offset for UTC+12
   * @returns Timezone offset string (+12:00)
   */
  static getTimezoneOffset(): string {
    return '+12:00';
  }
}
