// Mapper to convert between DTOs, Prisma models and Domain entities for UniToTslsDropbox
import { UniToTslsDropbox as PrismaUniToTslsDropbox } from '../../generated/prisma/index.js';
import { UniToTslsDropbox } from '../../domain/entities/index.js';
import {
  UniToTslsDropboxCreateDto,
  UniToTslsDropboxUpdateDto,
  UniToTslsDropboxResponseDto
} from '../dto/dropbox.dto.js';
import crypto from 'crypto';

export class UniToTslsDropboxMapper {
  // Convert from Prisma model to Domain entity
  static toDomain(prismaDropbox: PrismaUniToTslsDropbox): UniToTslsDropbox {
    return new UniToTslsDropbox(
      prismaDropbox.UniToTslsDropboxId,
      prismaDropbox.UniToTslsDropboxName,
      prismaDropbox.OpeningDate,
      prismaDropbox.ClosingDate,
      prismaDropbox.IsOpen,
      prismaDropbox.ReportType,
      prismaDropbox.UniversityId,
      prismaDropbox.CreatedAt,
      prismaDropbox.UpdatedAt || undefined
    );
  }

  // Convert from Domain entity to Prisma model data
  static toPrisma(domainDropbox: UniToTslsDropbox): Omit<PrismaUniToTslsDropbox, 'UniToTslsReportSubmissions' | 'University'> {
    return {
      UniToTslsDropboxId: domainDropbox.uniToTslsDropboxId,
      UniToTslsDropboxName: domainDropbox.uniToTslsDropboxName,
      CreatedAt: domainDropbox.createdAt,
      UpdatedAt: domainDropbox.updatedAt || null,
      OpeningDate: domainDropbox.openingDate,
      ClosingDate: domainDropbox.closingDate,
      IsOpen: domainDropbox.isOpen,
      ReportType: domainDropbox.reportType,
      UniversityId: domainDropbox.universityId
    };
  }

  // Convert from CreateDto to Domain entity
  static createDtoToDomain(createDto: UniToTslsDropboxCreateDto): UniToTslsDropbox {
    return new UniToTslsDropbox(
      crypto.randomUUID(),
      createDto.uniToTslsDropboxName,
      createDto.openingDate,
      createDto.closingDate,
      createDto.isOpen,
      createDto.reportType,
      createDto.universityId,
      new Date(),
      undefined
    );
  }

  // Convert from UpdateDto to Domain entity (partial update)
  static updateDtoToDomain(
    updateDto: UniToTslsDropboxUpdateDto,
    existingDropbox: UniToTslsDropbox
  ): UniToTslsDropbox {
    return new UniToTslsDropbox(
      existingDropbox.uniToTslsDropboxId,
      existingDropbox.uniToTslsDropboxName,
      updateDto.openingDate,
      updateDto.closingDate,
      updateDto.isOpen,
      existingDropbox.reportType,
      existingDropbox.universityId,
      existingDropbox.createdAt,
      new Date()
    );
  }

  // Convert from Domain entity to ResponseDto
  static domainToResponseDto(domainDropbox: UniToTslsDropbox): UniToTslsDropboxResponseDto {
    return {
      uniToTslsDropboxId: domainDropbox.uniToTslsDropboxId,
      uniToTslsDropboxName: domainDropbox.uniToTslsDropboxName,
      createdAt: domainDropbox.createdAt,
      updatedAt: domainDropbox.updatedAt,
      openingDate: domainDropbox.openingDate,
      closingDate: domainDropbox.closingDate,
      isOpen: domainDropbox.isOpen,
      reportType: domainDropbox.reportType,
      universityId: domainDropbox.universityId
    };
  }

  // Convert from Prisma model to ResponseDto (direct conversion)
  static prismaToResponseDto(prismaDropbox: PrismaUniToTslsDropbox): UniToTslsDropboxResponseDto {
    return {
      uniToTslsDropboxId: prismaDropbox.UniToTslsDropboxId,
      uniToTslsDropboxName: prismaDropbox.UniToTslsDropboxName,
      createdAt: prismaDropbox.CreatedAt,
      updatedAt: prismaDropbox.UpdatedAt || undefined,
      openingDate: prismaDropbox.OpeningDate,
      closingDate: prismaDropbox.ClosingDate,
      isOpen: prismaDropbox.IsOpen,
      reportType: prismaDropbox.ReportType,
      universityId: prismaDropbox.UniversityId
    };
  }

  // Convert array of Prisma models to Domain entities
  static toDomainArray(prismaDropboxes: PrismaUniToTslsDropbox[]): UniToTslsDropbox[] {
    return prismaDropboxes.map(dropbox => this.toDomain(dropbox));
  }

  // Convert array of Domain entities to ResponseDtos
  static domainArrayToResponseDtoArray(domainDropboxes: UniToTslsDropbox[]): UniToTslsDropboxResponseDto[] {
    return domainDropboxes.map(dropbox => this.domainToResponseDto(dropbox));
  }

  // Convert array of Prisma models to ResponseDtos
  static prismaArrayToResponseDtoArray(prismaDropboxes: PrismaUniToTslsDropbox[]): UniToTslsDropboxResponseDto[] {
    return prismaDropboxes.map(dropbox => this.prismaToResponseDto(dropbox));
  }
}
