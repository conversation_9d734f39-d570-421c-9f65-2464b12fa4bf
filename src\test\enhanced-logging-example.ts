// Enhanced Logging System Example
// This file demonstrates how to use the enhanced logging system with better descriptions and user details

import { LogService } from '../application/services/Log.Service.js';
import { LogRepository } from '../infrastructure/repositories/LogRepository.js';

// Example of how to use the enhanced logging system
export class EnhancedLoggingExample {
  private logService: LogService;

  constructor() {
    const logRepository = new LogRepository();
    this.logService = new LogService(logRepository);
  }

  // Example 1: Log a user login with enhanced details
  async logUserLogin(userId: string, ipAddress: string, userAgent: string) {
    // Parse user agent for device and browser info
    const mockRequest = {
      headers: { 'user-agent': userAgent },
      ip: ipAddress,
      method: 'POST',
      url: '/api/auth/login'
    };

    const log = await this.logService.createLogFromRequest(
      mockRequest,
      'User authentication successful',
      userId,
      'Login via web interface'
    );

    console.log('✅ Login logged:', {
      id: log.id,
      description: log.description,
      userDisplay: log.getUserDisplayName(),
      formattedDescription: log.getFormattedDescription(),
      deviceInfo: log.deviceInfo,
      browserInfo: log.browserInfo
    });

    return log;
  }

  // Example 2: Log user actions with context
  async logUserAction(userId: string, action: string, resource: string, details?: string) {
    const log = await this.logService.logUserAction(
      userId,
      action,
      resource,
      details,
      '*************',
      'Desktop (Windows 11)',
      'Chrome 120.0.0.0'
    );

    console.log('✅ User action logged:', {
      id: log.id,
      description: log.description,
      userDisplay: log.getUserDisplayName(),
      formattedDescription: log.getFormattedDescription()
    });

    return log;
  }

  // Example 3: Log authentication events
  async logAuthEvent(
    userId: string | null, 
    event: 'LOGIN' | 'LOGOUT' | 'LOGIN_FAILED' | 'REGISTER' | 'PASSWORD_RESET',
    ipAddress: string,
    details?: string
  ) {
    const log = await this.logService.logAuthEvent(
      userId,
      event,
      ipAddress,
      'Mobile (iOS 17.0)',
      'Safari 17.0',
      details
    );

    console.log('✅ Auth event logged:', {
      id: log.id,
      description: log.description,
      userDisplay: log.getUserDisplayName(),
      formattedDescription: log.getFormattedDescription()
    });

    return log;
  }

  // Example 4: Demonstrate different log descriptions
  async demonstrateLogDescriptions() {
    console.log('\n🔍 Enhanced Logging System Demonstration\n');

    // Example user ID (in real app, this would come from authentication)
    const userId = '123e4567-e89b-12d3-a456-************';

    // 1. User Login
    console.log('1. User Login Example:');
    await this.logUserLogin(
      userId,
      '************',
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    );

    // 2. User Actions
    console.log('\n2. User Action Examples:');
    await this.logUserAction(userId, 'CREATE', 'user', 'New student account');
    await this.logUserAction(userId, 'UPDATE', 'profile', 'Changed profile picture');
    await this.logUserAction(userId, 'DELETE', 'document', 'Removed old report');
    await this.logUserAction(userId, 'DOWNLOAD', 'report', 'Monthly statistics report');

    // 3. Authentication Events
    console.log('\n3. Authentication Event Examples:');
    await this.logAuthEvent(userId, 'LOGIN', '********', 'Successful login from mobile app');
    await this.logAuthEvent(null, 'LOGIN_FAILED', '********', 'Invalid email address');
    await this.logAuthEvent(userId, 'PASSWORD_RESET', '********', 'Password reset via email');
    await this.logAuthEvent(userId, 'LOGOUT', '********', 'Session expired');

    console.log('\n✅ Enhanced logging demonstration completed!');
  }
}

// Usage example:
// const example = new EnhancedLoggingExample();
// await example.demonstrateLogDescriptions();

/*
Expected Output Examples:

1. User Login:
   - Description: "User authentication successful via POST /api/auth/login"
   - User Display: "John Doe" (if user data is available)
   - Formatted: "John Doe - User authentication successful via POST /api/auth/login from Desktop (Windows 11) using Chrome 120.0.0.0"

2. User Actions:
   - CREATE user: "John Doe - Created user (New student account) from Desktop (Windows 11) using Chrome 120.0.0.0"
   - UPDATE profile: "John Doe - Updated profile (Changed profile picture) from Desktop (Windows 11) using Chrome 120.0.0.0"
   - DELETE document: "John Doe - Deleted document (Removed old report) from Desktop (Windows 11) using Chrome 120.0.0.0"

3. Authentication Events:
   - LOGIN: "John Doe - Successfully logged in - Successful login from mobile app from Mobile (iOS 17.0) using Safari 17.0"
   - LOGIN_FAILED: "System - Failed login attempt - Invalid email address from Mobile (iOS 17.0) using Safari 17.0"
   - PASSWORD_RESET: "John Doe - Password reset requested - Password reset via email from Mobile (iOS 17.0) using Safari 17.0"
*/
