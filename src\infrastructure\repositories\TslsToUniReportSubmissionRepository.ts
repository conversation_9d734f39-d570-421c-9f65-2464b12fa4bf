// TSLS to University Report Submission Repository - Data access layer for TSLS to University report submissions
import { PrismaClient } from '../../generated/prisma/index.js';
import { TslsToUniReportSubmission } from '../../domain/index.entity.js';
import { TslsToUniReportSubmissionMapper } from '../../helpers/mappers.js';
import { ITslsToUniReportSubmissionRepository } from '../../application/interfaces/IRepositories.js';
import { UTCTimeHelper } from '../../helpers/utcTimeHelper.js';

/**
 * TSLS to University Report Submission Repository Implementation
 * Equivalent to C# TslsToUniReportSubmissionRepository
 */
export class TslsToUniReportSubmissionRepository implements ITslsToUniReportSubmissionRepository {
  constructor(private prisma: PrismaClient) {}

  /**
   * Creates a new report submission
   * @param submission - Report submission to create
   */
  async createAsync(submission: TslsToUniReportSubmission): Promise<TslsToUniReportSubmission> {
    const prismaData = TslsToUniReportSubmissionMapper.toPrisma(submission);
    const createdSubmission = await this.prisma.tslsToUniReportSubmission.create({
      data: prismaData
    });

    return TslsToUniReportSubmissionMapper.toDomain(createdSubmission);
  }

  /**
   * Gets a report submission by ID
   * @param id - Submission ID
   */
  async getByIdAsync(id: string): Promise<TslsToUniReportSubmission | null> {
    const prismaSubmission = await this.prisma.tslsToUniReportSubmission.findUnique({
      where: { TslsToUniReportSubmissionId: id }
    });

    return prismaSubmission ? TslsToUniReportSubmissionMapper.toDomain(prismaSubmission) : null;
  }

  /**
   * Gets report submissions by dropbox ID
   * @param dropboxId - Dropbox ID
   */
  async getByDropboxIdAsync(dropboxId: string): Promise<TslsToUniReportSubmission[]> {
    const prismaSubmissions = await this.prisma.tslsToUniReportSubmission.findMany({
      where: { TslsToUniDropboxId: dropboxId },
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions);
  }

  /**
   * Gets report submissions by user ID
   * @param userId - User ID
   */
  async getByUserIdAsync(userId: string): Promise<TslsToUniReportSubmission[]> {
    const prismaSubmissions = await this.prisma.tslsToUniReportSubmission.findMany({
      where: { SubmittedBy: userId },
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions);
  }

  /**
   * Gets all report submissions
   */
  async getAllAsync(): Promise<TslsToUniReportSubmission[]> {
    const prismaSubmissions = await this.prisma.tslsToUniReportSubmission.findMany({
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions);
  }

  /**
   * Gets report submissions by date range
   * @param startDate - Start date
   * @param endDate - End date
   */
  async getByDateRangeAsync(startDate: Date, endDate: Date): Promise<TslsToUniReportSubmission[]> {
    const prismaSubmissions = await this.prisma.tslsToUniReportSubmission.findMany({
      where: {
        SubmittedAt: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions);
  }

  /**
   * Gets report submissions with pagination
   * @param page - Page number (1-based)
   * @param limit - Items per page
   */
  async getPaginatedAsync(page: number, limit: number): Promise<{ submissions: TslsToUniReportSubmission[], total: number }> {
    const skip = (page - 1) * limit;

    const [prismaSubmissions, total] = await Promise.all([
      this.prisma.tslsToUniReportSubmission.findMany({
        skip,
        take: limit,
        orderBy: { SubmittedAt: 'desc' }
      }),
      this.prisma.tslsToUniReportSubmission.count()
    ]);

    return {
      submissions: TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions),
      total
    };
  }

  /**
   * Gets report submissions by dropbox with pagination
   * @param dropboxId - Dropbox ID
   * @param page - Page number (1-based)
   * @param limit - Items per page
   */
  async getByDropboxPaginatedAsync(dropboxId: string, page: number, limit: number): Promise<{ submissions: TslsToUniReportSubmission[], total: number }> {
    const skip = (page - 1) * limit;

    const [prismaSubmissions, total] = await Promise.all([
      this.prisma.tslsToUniReportSubmission.findMany({
        where: { TslsToUniDropboxId: dropboxId },
        skip,
        take: limit,
        orderBy: { SubmittedAt: 'desc' }
      }),
      this.prisma.tslsToUniReportSubmission.count({
        where: { TslsToUniDropboxId: dropboxId }
      })
    ]);

    return {
      submissions: TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions),
      total
    };
  }

  /**
   * Updates a report submission
   * @param submission - Submission to update
   */
  async updateAsync(submission: TslsToUniReportSubmission): Promise<void> {
    const prismaData = TslsToUniReportSubmissionMapper.toPrisma(submission);
    await this.prisma.tslsToUniReportSubmission.update({
      where: { TslsToUniReportSubmissionId: submission.tslsToUniReportSubmissionId },
      data: prismaData
    });
  }

  /**
   * Deletes a report submission
   * @param id - Submission ID
   */
  async deleteAsync(id: string): Promise<void> {
    await this.prisma.tslsToUniReportSubmission.delete({
      where: { TslsToUniReportSubmissionId: id }
    });
  }

  /**
   * Checks if a submission exists
   * @param id - Submission ID
   */
  async existsAsync(id: string): Promise<boolean> {
    const count = await this.prisma.tslsToUniReportSubmission.count({
      where: { TslsToUniReportSubmissionId: id }
    });
    return count > 0;
  }

  /**
   * Gets recent submissions
   * @param daysBack - Number of days back to check
   */
  async getRecentSubmissionsAsync(daysBack: number = 30): Promise<TslsToUniReportSubmission[]> {
    const pastDate = UTCTimeHelper.subtractDays(UTCTimeHelper.now(), daysBack);

    const prismaSubmissions = await this.prisma.tslsToUniReportSubmission.findMany({
      where: {
        SubmittedAt: { gte: pastDate }
      },
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions);
  }

  /**
   * Gets submission count by dropbox
   * @param dropboxId - Dropbox ID
   */
  async ********************************(dropboxId: string): Promise<number> {
    return await this.prisma.tslsToUniReportSubmission.count({
      where: { TslsToUniDropboxId: dropboxId }
    });
  }

  /**
   * Gets submission count by user
   * @param userId - User ID
   */
  async getSubmissionCountByUserAsync(userId: string): Promise<number> {
    return await this.prisma.tslsToUniReportSubmission.count({
      where: { SubmittedBy: userId }
    });
  }

  /**
   * Gets submissions with user details
   */
  async getSubmissionsWithUserDetailsAsync(): Promise<any[]> {
    const submissions = await this.prisma.tslsToUniReportSubmission.findMany({
      include: {
        PTEIUser: {
          select: {
            FirstName: true,
            LastName: true,
            email: true
          }
        },
        TslsToUniDropbox: {
          select: {
            TslsToUniDropboxName: true
          }
        }
      },
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.prismaWithUserArrayToResponseDtoArray(submissions);
  }

  /**
   * Searches submissions by name
   * @param searchTerm - Search term
   */
  async searchByNameAsync(searchTerm: string): Promise<TslsToUniReportSubmission[]> {
    const prismaSubmissions = await this.prisma.tslsToUniReportSubmission.findMany({
      where: {
        TslsToUniReportSubmissionName: {
          contains: searchTerm,
          mode: 'insensitive'
        }
      },
      orderBy: { SubmittedAt: 'desc' }
    });

    return TslsToUniReportSubmissionMapper.toDomainArray(prismaSubmissions);
  }
}
