export const schedulerConfig = {
  // Cron expressions for different schedules
  REPORT_SETTINGS: {
    // Every hour at minute 0
    HOURLY: '0 * * * *',
    // Every 30 minutes
    HALF_HOURLY: '*/30 * * * *',
    // Every 15 minutes
    QUARTER_HOURLY: '*/15 * * * *',
    // Every day at 6 AM
    DAILY_6AM: '0 6 * * *',
    // Every weekday at 8 AM
    WEEKDAY_8AM: '0 8 * * 1-5'
  },
  
  // Timezone configuration
  TIMEZONE: 'Pacific/Fiji',
  
  // Enable/disable schedulers
  ENABLED: {
    REPORT_SETTINGS: process.env.ENABLE_REPORT_SCHEDULER !== 'false'
  }
};