// TslsToUniReportSubmission Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { TslsToUniReportSubmissionService } from '../../application/services/TslsToUniReportSubmission.Service.js';
import { TslsToUniReportSubmissionCreateDto } from '../../application/dto/dropbox.dto.js';

export class TslsToUniReportSubmissionController {
  private submissionService: TslsToUniReportSubmissionService;

  constructor(prisma: any) {
    this.submissionService = new TslsToUniReportSubmissionService(prisma);
  }

  /**
   * Submit TSLS to University report with file upload
   */
  async submitTslsToUniReport(request: FastifyRequest, reply: FastifyReply) {
    try {
      // Get user ID from JWT token
      const userId = (request as any).user?.userId;
      if (!userId) {
        return reply.status(401).send({ 
          success: false, 
          message: 'User authentication required' 
        });
      }

      // Handle multipart form data
      const data = await request.file();
      if (!data) {
        return reply.status(400).send({ 
          success: false, 
          message: 'File is required' 
        });
      }

      // Extract form fields
      const fields = data.fields as any;
      const tslsToUniReportSubmissionName = fields.tslsToUniReportSubmissionName?.value;
      const tslsToUniDropboxId = fields.tslsToUniDropboxId?.value;
      const notes = fields.notes?.value || '';

      // Validate required fields
      if (!tslsToUniReportSubmissionName) {
        return reply.status(400).send({ 
          success: false, 
          message: 'Report submission name is required' 
        });
      }

      if (!tslsToUniDropboxId) {
        return reply.status(400).send({ 
          success: false, 
          message: 'Dropbox ID is required' 
        });
      }

      // Create DTO
      const createDto: TslsToUniReportSubmissionCreateDto = {
        tslsToUniReportSubmissionName,
        file: data, // Pass the file object
        notes,
        tslsToUniDropboxId
      };

      // Create submission
      const result = await this.submissionService.createSubmission(createDto, userId, data);

      return reply.status(201).send({
        success: true,
        message: 'TSLS to University report submitted successfully',
        data: result
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      // Handle specific error types
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({ success: false, message });
      }
      
      if (message.toLowerCase().includes('not open') || 
          message.toLowerCase().includes('not accepting') ||
          message.toLowerCase().includes('not within')) {
        return reply.status(400).send({ success: false, message });
      }

      if (message.toLowerCase().includes('invalid file')) {
        return reply.status(400).send({ success: false, message });
      }

      return reply.status(500).send({ 
        success: false, 
        message: 'Internal server error while submitting report' 
      });
    }
  }

  /**
   * Get submissions by dropbox ID with pagination
   */
  async getSubmissionsByDropbox(request: FastifyRequest<{ 
    Params: { dropboxId: string }, 
    Querystring: { page?: string; limit?: string } 
  }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const page = request.query.page ? parseInt(request.query.page, 10) : 1;
      const limit = request.query.limit ? parseInt(request.query.limit, 10) : 10;

      // Validate pagination parameters
      if (page < 1 || limit < 1 || limit > 100) {
        return reply.status(400).send({ 
          success: false, 
          message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100' 
        });
      }

      const result = await this.submissionService.getSubmissionsByDropbox(dropboxId, page, limit);
      
      return reply.status(200).send({ 
        success: true, 
        data: {
          submissions: result.submissions,
          pagination: {
            page,
            limit,
            total: result.total,
            totalPages: Math.ceil(result.total / limit)
          }
        }
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({ success: false, message });
      }
      
      return reply.status(500).send({ 
        success: false, 
        message: 'Internal server error while retrieving submissions' 
      });
    }
  }

  /**
   * Get submission by ID
   */
  async getSubmissionById(request: FastifyRequest<{ 
    Params: { submissionId: string } 
  }>, reply: FastifyReply) {
    try {
      const { submissionId } = request.params;
      
      const result = await this.submissionService.getSubmissionById(submissionId);
      
      if (!result) {
        return reply.status(404).send({ 
          success: false, 
          message: 'Submission not found' 
        });
      }

      return reply.status(200).send({ 
        success: true, 
        data: result 
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ 
        success: false, 
        message: 'Internal server error while retrieving submission' 
      });
    }
  }

  /**
   * Delete submission by ID
   */
  async deleteSubmission(request: FastifyRequest<{ 
    Params: { submissionId: string } 
  }>, reply: FastifyReply) {
    try {
      const { submissionId } = request.params;
      const userId = (request as any).user?.userId;

      if (!userId) {
        return reply.status(401).send({ 
          success: false, 
          message: 'User authentication required' 
        });
      }

      await this.submissionService.deleteSubmission(submissionId, userId);
      
      return reply.status(200).send({ 
        success: true, 
        message: 'TSLS to University report submission deleted successfully' 
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      
      if (message.toLowerCase().includes('not found')) {
        return reply.status(404).send({ success: false, message });
      }
      
      if (message.toLowerCase().includes('unauthorized') || 
          message.toLowerCase().includes('permission')) {
        return reply.status(403).send({ success: false, message });
      }

      return reply.status(500).send({ 
        success: false, 
        message: 'Internal server error while deleting submission' 
      });
    }
  }

  /**
   * Get all submissions with pagination
   */
  async getAllSubmissions(request: FastifyRequest<{ 
    Querystring: { page?: string; limit?: string } 
  }>, reply: FastifyReply) {
    try {
      const page = request.query.page ? parseInt(request.query.page, 10) : 1;
      const limit = request.query.limit ? parseInt(request.query.limit, 10) : 10;

      // Validate pagination parameters
      if (page < 1 || limit < 1 || limit > 100) {
        return reply.status(400).send({ 
          success: false, 
          message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100' 
        });
      }

      const result = await this.submissionService.getAllSubmissions(page, limit);
      
      return reply.status(200).send({ 
        success: true, 
        data: {
          submissions: result.submissions,
          pagination: {
            page,
            limit,
            total: result.total,
            totalPages: Math.ceil(result.total / limit)
          }
        }
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ 
        success: false, 
        message: 'Internal server error while retrieving submissions' 
      });
    }
  }

  /**
   * Get submissions by user ID
   */
  async getSubmissionsByUser(request: FastifyRequest<{ 
    Params: { userId: string },
    Querystring: { page?: string; limit?: string } 
  }>, reply: FastifyReply) {
    try {
      const { userId } = request.params;
      const page = request.query.page ? parseInt(request.query.page, 10) : 1;
      const limit = request.query.limit ? parseInt(request.query.limit, 10) : 10;

      // Validate pagination parameters
      if (page < 1 || limit < 1 || limit > 100) {
        return reply.status(400).send({ 
          success: false, 
          message: 'Invalid pagination parameters. Page must be >= 1, limit must be between 1 and 100' 
        });
      }

      const result = await this.submissionService.getSubmissionsByUser(userId, page, limit);
      
      return reply.status(200).send({ 
        success: true, 
        data: {
          submissions: result.submissions,
          pagination: {
            page,
            limit,
            total: result.total,
            totalPages: Math.ceil(result.total / limit)
          }
        }
      });

    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({ 
        success: false, 
        message: 'Internal server error while retrieving user submissions' 
      });
    }
  }
}
