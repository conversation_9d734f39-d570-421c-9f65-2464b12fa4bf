// TslsToUniReportSubmission routes
import { FastifyInstance } from 'fastify';
import { TslsToUniReportSubmissionController } from '../controllers/TslsToUniReportSubmission.Controller.js';
import {
  TslsToUniReportSubmissionCreateDto,
  TslsToUniReportSubmissionCreateSchema
} from '../../application/dto/dropbox.dto.js';
import { verifyJWT } from '../middleware/auth.js';

export default async function tslsToUniReportSubmissionRoutes(fastify: FastifyInstance) {
  const submissionController = new TslsToUniReportSubmissionController(fastify.prisma);

  // Swagger schemas for documentation
  const submissionResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: {
        type: 'object',
        properties: {
          tslsToUniReportSubmissionId: { type: 'string' },
          tslsToUniReportSubmissionName: { type: 'string' },
          tslsToUniReportSubmissionFilePath: { type: 'string' },
          submittedAt: { type: 'string', format: 'date-time' },
          notes: { type: 'string' },
          submittedBy: { type: 'string' },
          tslsToUniDropboxId: { type: 'string' }
        }
      }
    }
  };

  const errorResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      errors: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  const paginatedResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      data: {
        type: 'object',
        properties: {
          submissions: {
            type: 'array',
            items: submissionResponseSchema.properties.data
          },
          pagination: {
            type: 'object',
            properties: {
              page: { type: 'number' },
              limit: { type: 'number' },
              total: { type: 'number' },
              totalPages: { type: 'number' }
            }
          }
        }
      }
    }
  };

  // Submit TSLS to University report (multipart form data for file upload)
  fastify.post('/submit-tsls-to-uni-report', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Submit TSLS to University report with file upload',
      tags: ['TSLS to University Report Submission'],
      security: [{ bearerAuth: [] }],
      consumes: ['multipart/form-data'],
      body: {
        type: 'object',
        required: ['tslsToUniReportSubmissionName', 'tslsToUniDropboxId', 'file'],
        properties: {
          tslsToUniReportSubmissionName: { 
            type: 'string', 
            minLength: 1, 
            maxLength: 1000,
            description: 'Name of the report submission'
          },
          notes: { 
            type: 'string', 
            maxLength: 2000,
            description: 'Optional notes for the submission'
          },
          tslsToUniDropboxId: { 
            type: 'string', 
            format: 'uuid',
            description: 'ID of the TSLS to University dropbox'
          },
          file: {
            type: 'string',
            format: 'binary',
            description: 'File to upload (PDF, DOC, DOCX, XLS, XLSX, PPT, PPTX)'
          }
        }
      },
      response: {
        201: submissionResponseSchema,
        400: errorResponseSchema,
        401: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, submissionController.submitTslsToUniReport.bind(submissionController));

  // Get submissions by dropbox ID
  fastify.get<{ Params: { dropboxId: string } }>('/get-submissions/:dropboxId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get TSLS to University report submissions by dropbox ID',
      tags: ['TSLS to University Report Submission'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          dropboxId: { type: 'string', format: 'uuid' }
        },
        required: ['dropboxId']
      },
      querystring: {
        type: 'object',
        properties: {
          page: { 
            type: 'string', 
            pattern: '^[1-9]\\d*$',
            description: 'Page number (default: 1)'
          },
          limit: { 
            type: 'string', 
            pattern: '^[1-9]\\d*$',
            description: 'Items per page (default: 10, max: 100)'
          }
        }
      },
      response: {
        200: paginatedResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, submissionController.getSubmissionsByDropbox.bind(submissionController));

  // Get submission by ID
  fastify.get<{ Params: { submissionId: string } }>('/get-submission/:submissionId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get TSLS to University report submission by ID',
      tags: ['TSLS to University Report Submission'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          submissionId: { type: 'string', format: 'uuid' }
        },
        required: ['submissionId']
      },
      response: {
        200: submissionResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, submissionController.getSubmissionById.bind(submissionController));

  // Get all submissions with pagination
  fastify.get('/get-all-submissions', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get all TSLS to University report submissions with pagination',
      tags: ['TSLS to University Report Submission'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { 
            type: 'string', 
            pattern: '^[1-9]\\d*$',
            description: 'Page number (default: 1)'
          },
          limit: { 
            type: 'string', 
            pattern: '^[1-9]\\d*$',
            description: 'Items per page (default: 10, max: 100)'
          }
        }
      },
      response: {
        200: paginatedResponseSchema,
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, submissionController.getAllSubmissions.bind(submissionController));

  // Get submissions by user ID
  fastify.get<{ Params: { userId: string } }>('/get-submissions-by-user/:userId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get TSLS to University report submissions by user ID',
      tags: ['TSLS to University Report Submission'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          userId: { type: 'string', format: 'uuid' }
        },
        required: ['userId']
      },
      querystring: {
        type: 'object',
        properties: {
          page: { 
            type: 'string', 
            pattern: '^[1-9]\\d*$',
            description: 'Page number (default: 1)'
          },
          limit: { 
            type: 'string', 
            pattern: '^[1-9]\\d*$',
            description: 'Items per page (default: 10, max: 100)'
          }
        }
      },
      response: {
        200: paginatedResponseSchema,
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, submissionController.getSubmissionsByUser.bind(submissionController));

  // Delete submission
  fastify.delete<{ Params: { submissionId: string } }>('/delete-submission/:submissionId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Delete TSLS to University report submission',
      tags: ['TSLS to University Report Submission'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          submissionId: { type: 'string', format: 'uuid' }
        },
        required: ['submissionId']
      },
      response: {
        200: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' }
          }
        },
        401: errorResponseSchema,
        403: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, submissionController.deleteSubmission.bind(submissionController));
}
