// UniToTslsDropbox routes
import { FastifyInstance } from 'fastify';
import { UniToTslsDropboxController } from '../controllers/UniToTslsDropbox.Controller.js';
import {
  UniToTslsDropboxCreateDto,
  UniToTslsDropboxUpdateDto
} from '../../application/dto/dropbox.dto.js';
import { verifyJWT } from '../middleware/auth.js';

export default async function uniToTslsDropboxRoutes(fastify: FastifyInstance) {
  const dropboxController = new UniToTslsDropboxController(fastify.prisma);

  // Swagger schemas for documentation
  const dropboxResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      data: {
        type: 'object',
        properties: {
          uniToTslsDropboxId: { type: 'string' },
          uniToTslsDropboxName: { type: 'string' },
          createdAt: { type: 'string', format: 'date-time' },
          updatedAt: { type: 'string', format: 'date-time' },
          openingDate: { type: 'string', format: 'date-time' },
          closingDate: { type: 'string', format: 'date-time' },
          isOpen: { type: 'boolean' }
        }
      }
    }
  };

  const errorResponseSchema = {
    type: 'object',
    properties: {
      success: { type: 'boolean' },
      message: { type: 'string' },
      errors: {
        type: 'array',
        items: { type: 'string' }
      }
    }
  };

  // Create Uni to TSLS dropbox
  fastify.post<{ Body: UniToTslsDropboxCreateDto }>('/create-uni-to-tsls-dropbox', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Create University to TSLS dropbox',
      tags: ['University to TSLS Dropbox'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['uniToTslsDropboxName', 'openingDate', 'closingDate', 'isOpen', 'reportType', 'universityId'],
        properties: {
          uniToTslsDropboxName: { type: 'string', minLength: 1 },
          openingDate: { type: 'string', format: 'date-time' },
          closingDate: { type: 'string', format: 'date-time' },
          isOpen: { type: 'boolean' },
          reportType: { type: 'string', default: 'Standard' },
          universityId: { type: 'string', format: 'uuid' }
        }
      },
      response: {
        201: dropboxResponseSchema,
        400: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, dropboxController.createUniToTslsDropbox.bind(dropboxController));

  // Update Uni to TSLS dropbox
  fastify.put<{
    Params: { dropboxId: string };
    Body: UniToTslsDropboxUpdateDto;
  }>('/update-uni-to-tsls-dropbox/:dropboxId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Update University to TSLS dropbox',
      tags: ['University to TSLS Dropbox'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          dropboxId: { type: 'string', format: 'uuid' }
        },
        required: ['dropboxId']
      },
      body: {
        type: 'object',
        required: ['openingDate', 'closingDate', 'isOpen'],
        properties: {
          openingDate: { type: 'string', format: 'date-time' },
          closingDate: { type: 'string', format: 'date-time' },
          isOpen: { type: 'boolean' }
        }
      },
      response: {
        200: dropboxResponseSchema,
        400: errorResponseSchema,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, dropboxController.updateUniToTslsDropbox.bind(dropboxController));

  // Get individual Uni to TSLS dropbox
  fastify.get<{ Params: { dropboxId: string } }>('/get-individual-uni-to-tsls-dropbox/:dropboxId', {
    preHandler: [verifyJWT],
    schema: {
      description: 'Get University to TSLS dropbox by ID',
      tags: ['University to TSLS Dropbox'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        properties: {
          dropboxId: { type: 'string', format: 'uuid' }
        },
        required: ['dropboxId']
      },
      response: {
        200: dropboxResponseSchema.properties.data,
        404: errorResponseSchema,
        500: errorResponseSchema
      }
    }
  }, dropboxController.getIndividualUniToTslsDropbox.bind(dropboxController));
}
