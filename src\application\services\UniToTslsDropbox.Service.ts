// UniToTslsDropbox Service
import { PrismaClient } from '../../generated/prisma/index.js';
import { UniToTslsDropbox } from '../../domain/index.entity.js';
import {
  UniToTslsDropboxDto,
  UniToTslsDropboxCreateDto,
  UniToTslsDropboxUpdateDto,
  UniToTslsDropboxCreateSchema
} from '../dto/dropbox.dto.js';
import { IUniToTslsDropboxService } from '../interfaces/IServices.js';
import { UTCTimeHelper } from '../../helpers/utcTimeHelper.js';
import { DtoValidator } from '../validation/DtoValidator.js';
import { UniToTslsDropboxMapper } from '../mapppers/UniToTslsDropboxMapper.js';
import crypto from 'crypto';

export class UniToTslsDropboxService implements IUniToTslsDropboxService {
  constructor(private prisma: PrismaClient) {}

  async createAsync(userId: string, createDto: UniToTslsDropboxCreateDto): Promise<UniToTslsDropboxDto> {
    // Validate DTO
    const validatedDto = DtoValidator.validate(createDto, UniToTslsDropboxCreateSchema);

    // Sanitize input
    const sanitizedDto = DtoValidator.sanitizeUserInput(validatedDto);

    // Check if dropbox already exists with same name
    const existingDropbox = await this.prisma.uniToTslsDropbox.findFirst({
      where: {
        UniToTslsDropboxName: sanitizedDto.uniToTslsDropboxName,
        IsOpen: true
      }
    });

    if (existingDropbox) {
      throw new InvalidOperationException('An active dropbox with this name already exists');
    }

    const dropbox = new UniToTslsDropbox(
      crypto.randomUUID(),
      sanitizedDto.uniToTslsDropboxName,
      sanitizedDto.openingDate,
      sanitizedDto.closingDate,
      sanitizedDto.isOpen,
      UTCTimeHelper.now(),
      UTCTimeHelper.now()
    );

    // Validate business rules
    if (!dropbox.isValidDateRange()) {
      throw new Error('Invalid date range: opening date must be before closing date');
    }

    const createdDropbox = await this.prisma.uniToTslsDropbox.create({
      data: {
        UniToTslsDropboxId: dropbox.uniToTslsDropboxId,
        UniToTslsDropboxName: dropbox.uniToTslsDropboxName,
        OpeningDate: dropbox.openingDate,
        ClosingDate: dropbox.closingDate,
        IsOpen: dropbox.isOpen,
        CreatedAt: dropbox.createdAt,
        UpdatedAt: dropbox.updatedAt,
        ReportType: 'Standard', // Default value
        UniversityId: '00000000-0000-0000-0000-000000000000' // Default placeholder UUID
      }
    });

    return {
      uniToTslsDropboxId: createdDropbox.UniToTslsDropboxId,
      uniToTslsDropboxName: createdDropbox.UniToTslsDropboxName,
      openingDate: createdDropbox.OpeningDate,
      closingDate: createdDropbox.ClosingDate,
      isOpen: createdDropbox.IsOpen,
      createdAt: createdDropbox.CreatedAt,
      updatedAt: createdDropbox.UpdatedAt || undefined
    };
  }

  async updateAsync(userId: string, dropboxId: string, updateDto: UniToTslsDropboxUpdateDto): Promise<UniToTslsDropboxDto> {
    const existingDropbox = await this.prisma.uniToTslsDropbox.findUnique({
      where: { UniToTslsDropboxId: dropboxId }
    });

    if (!existingDropbox) {
      throw new Error('Dropbox not found');
    }

    const updatedDropbox = await this.prisma.uniToTslsDropbox.update({
      where: { UniToTslsDropboxId: dropboxId },
      data: {
        UniToTslsDropboxName: updateDto.uniToTslsDropboxName ?? existingDropbox.UniToTslsDropboxName,
        OpeningDate: updateDto.openingDate ?? existingDropbox.OpeningDate,
        ClosingDate: updateDto.closingDate ?? existingDropbox.ClosingDate,
        IsOpen: updateDto.isOpen ?? existingDropbox.IsOpen,
        UpdatedAt: UTCTimeHelper.now()
      }
    });

    return {
      uniToTslsDropboxId: updatedDropbox.UniToTslsDropboxId,
      uniToTslsDropboxName: updatedDropbox.UniToTslsDropboxName,
      openingDate: updatedDropbox.OpeningDate,
      closingDate: updatedDropbox.ClosingDate,
      isOpen: updatedDropbox.IsOpen,
      createdAt: updatedDropbox.CreatedAt,
      updatedAt: updatedDropbox.UpdatedAt || undefined
    };
  }

  async getByIdAsync(dropboxId: string): Promise<UniToTslsDropboxDto> {
    const dropbox = await this.prisma.uniToTslsDropbox.findUnique({
      where: { UniToTslsDropboxId: dropboxId }
    });

    if (!dropbox) {
      throw new Error('Dropbox not found');
    }

    return {
      uniToTslsDropboxId: dropbox.UniToTslsDropboxId,
      uniToTslsDropboxName: dropbox.UniToTslsDropboxName,
      openingDate: dropbox.OpeningDate,
      closingDate: dropbox.ClosingDate,
      isOpen: dropbox.IsOpen,
      createdAt: dropbox.CreatedAt,
      updatedAt: dropbox.UpdatedAt || undefined
    };
  }
}

class InvalidOperationException extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'InvalidOperationException';
  }
}
