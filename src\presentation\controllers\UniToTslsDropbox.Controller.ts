// UniToTslsDropbox Controller
import { FastifyRequest, FastifyReply } from 'fastify';
import { UniToTslsDropboxService } from '../../application/services/UniToTslsDropbox.Service.js';
import { UniToTslsDropboxCreateDto, UniToTslsDropboxUpdateDto } from '../../application/dto/dropbox.dto.js';

export class UniToTslsDropboxController {
  private dropboxService: UniToTslsDropboxService;

  constructor(prisma: any) {
    this.dropboxService = new UniToTslsDropboxService(prisma);
  }

  async createUniToTslsDropbox(request: FastifyRequest<{ Body: UniToTslsDropboxCreateDto }>, reply: FastifyReply) {
    try {
      const createDto = request.body;
      // Get user ID from JWT token (assuming it's available in request.user)
      const userId = (request as any).user?.userId || 'system';

      if (!createDto.uniToTslsDropboxName || !createDto.openingDate || !createDto.closingDate) {
        return reply.status(400).send({ error: 'Missing required fields' });
      }

      const result = await this.dropboxService.createAsync(userId, createDto);
      return reply.status(201).send({
        success: true,
        message: 'University to TSLS dropbox created successfully',
        data: result
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';

      if (message.includes('already exists')) {
        return reply.status(409).send({
          success: false,
          error: message
        });
      }

      return reply.status(500).send({
        success: false,
        error: 'An error occurred while creating the dropbox.'
      });
    }
  }

  async updateUniToTslsDropbox(request: FastifyRequest<{
    Params: { dropboxId: string };
    Body: UniToTslsDropboxUpdateDto;
  }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const updateDto = request.body;
      // Get user ID from JWT token (assuming it's available in request.user)
      const userId = (request as any).user?.userId || 'system';

      const result = await this.dropboxService.updateAsync(userId, dropboxId, updateDto);
      return reply.status(200).send({
        success: true,
        message: 'University to TSLS dropbox updated successfully',
        data: result
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({
        success: false,
        error: message
      });
    }
  }

  async getIndividualUniToTslsDropbox(request: FastifyRequest<{ Params: { dropboxId: string } }>, reply: FastifyReply) {
    try {
      const { dropboxId } = request.params;
      const result = await this.dropboxService.getByIdAsync(dropboxId);
      return reply.status(200).send({
        success: true,
        data: result
      });
    } catch (error) {
      const message = error instanceof Error ? error.message : 'An error occurred';
      return reply.status(500).send({
        success: false,
        error: message
      });
    }
  }
}
