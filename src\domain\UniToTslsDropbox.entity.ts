// Domain Entity for UniToTslsDropbox
import { UTCTimeHelper } from '../helpers/utcTimeHelper.js';

export class UniToTslsDropbox {
  constructor(
    public readonly uniToTslsDropboxId: string,
    public readonly uniToTslsDropboxName: string,
    public readonly openingDate: Date,
    public readonly closingDate: Date,
    public readonly isOpen: boolean,
    public readonly reportType: string = 'Standard',
    public readonly universityId: string,
    public readonly createdAt: Date = UTCTimeHelper.now(),
    public readonly updatedAt?: Date
  ) {}

  // Domain methods
  public open(): UniToTslsDropbox {
    return new UniToTslsDropbox(
      this.uniToTslsDropboxId,
      this.uniToTslsDropboxName,
      this.openingDate,
      this.closingDate,
      true,
      this.reportType,
      this.universityId,
      this.createdAt,
      UTCTimeHelper.now()
    );
  }

  public close(): UniToTslsDropbox {
    return new UniToTslsDropbox(
      this.uniToTslsDropboxId,
      this.uniToTslsDropboxName,
      this.openingDate,
      this.closingDate,
      false,
      this.reportType,
      this.universityId,
      this.createdAt,
      UTCTimeHelper.now()
    );
  }

  public updateDates(openingDate: Date, closingDate: Date): UniToTslsDropbox {
    return new UniToTslsDropbox(
      this.uniToTslsDropboxId,
      this.uniToTslsDropboxName,
      openingDate,
      closingDate,
      this.isOpen,
      this.reportType,
      this.universityId,
      this.createdAt,
      UTCTimeHelper.now()
    );
  }

  // Business rules
  public isCurrentlyOpen(): boolean {
    const now = UTCTimeHelper.now();
    return this.isOpen &&
           now >= this.openingDate &&
           now <= this.closingDate;
  }

  public canAcceptSubmissions(): boolean {
    return this.isCurrentlyOpen();
  }

  public isValidDateRange(): boolean {
    return this.openingDate < this.closingDate;
  }

  public getDurationInDays(): number {
    const diffTime = Math.abs(this.closingDate.getTime() - this.openingDate.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }

  public getTimeUntilOpening(): number | null {
    const now = UTCTimeHelper.now();
    if (now >= this.openingDate) {
      return null; // Already opened or past opening
    }
    return this.openingDate.getTime() - now.getTime();
  }

  public getTimeUntilClosing(): number | null {
    const now = UTCTimeHelper.now();
    if (now >= this.closingDate) {
      return null; // Already closed
    }
    return this.closingDate.getTime() - now.getTime();
  }
}
