import { PrismaClient } from '../../generated/prisma/index.js';
import { ReportSettingsSchedulerService } from './ReportSettingsScheduler.Service.js';
import { schedulerConfig } from '../../infrastructure/config/scheduler.config.js';
import { env } from '../../infrastructure/config/env.js';

export class BackgroundJobSchedulerService {
  private reportSettingsScheduler: ReportSettingsSchedulerService;

  constructor(private prisma: PrismaClient) {
    this.reportSettingsScheduler = new ReportSettingsSchedulerService(prisma);
  }

  /**
   * Start all background job schedulers
   */
  startAll(): void {
    console.log('🚀 Starting all background job schedulers...');

    if (schedulerConfig.ENABLED.REPORT_SETTINGS) {
      this.reportSettingsScheduler.start(env.REPORT_SCHEDULER_CRON);
    }

    console.log('✅ All background job schedulers started');
  }

  /**
   * Stop all background job schedulers
   */
  stopAll(): void {
    console.log('🛑 Stopping all background job schedulers...');
    
    this.reportSettingsScheduler.stop();
    
    console.log('✅ All background job schedulers stopped');
  }

  /**
   * Get status of all schedulers
   */
  getStatus() {
    return {
      reportSettings: this.reportSettingsScheduler.getStatus()
    };
  }
}