// Dropbox DTOs - Using Zod for validation
import { z } from "zod";
import { ModeOfStudy } from '../../domain/enums.entity.js';

// ===== TSLS TO UNI DROPBOX DTOs =====

// TSLS to University Dropbox Response DTO Schema
export const TslsToUniDropboxResponseSchema = z.object({
  tslsToUniDropboxId: z.string().uuid("Invalid UUID format"),
  tslsToUniDropboxName: z.string(),
  universityId: z.string().uuid("Invalid UUID format"),
  year: z.number().int().min(2025).max(2100),
  modeOfStudy: z.nativeEnum(ModeOfStudy),
  term: z.number().int().min(1).max(4),
  batchNumber: z.number().int(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date().optional(),
  openingDate: z.coerce.date(),
  closingDate: z.coerce.date(),
  isOpenStatus: z.boolean(),
  reportType: z.string().default('Standard'),
});

export type TslsToUniDropboxResponseDto = z.infer<typeof TslsToUniDropboxResponseSchema>;

// TSLS to University Dropbox Create DTO Schema
export const TslsToUniDropboxCreateSchema = z.object({
  universityId: z.string().uuid("Invalid UUID format"),
  year: z.number().int().min(2025).max(2100),
  modeOfStudy: z.nativeEnum(ModeOfStudy),
  term: z.number().int().min(1).max(4),
  resetBatch: z.boolean().optional().default(false),
  openingDate: z.coerce.date(),
  closingDate: z.coerce.date(),
  isOpenStatus: z.boolean(),
});

export type TslsToUniDropboxCreateDto = z.infer<typeof TslsToUniDropboxCreateSchema>;

// TSLS to University Dropbox Update DTO Schema
export const TslsToUniDropboxUpdateSchema = z.object({
  openingDate: z.coerce.date(),
  closingDate: z.coerce.date(),
  isOpenStatus: z.boolean(),
});

export type TslsToUniDropboxUpdateDto = z.infer<typeof TslsToUniDropboxUpdateSchema>;

// ===== TSLS TO UNI REPORT SUBMISSION DTOs =====

// TSLS to University Report Submission Response DTO Schema
export const TslsToUniReportSubmissionResponseSchema = z.object({
  tslsToUniReportSubmissionId: z.string().uuid("Invalid UUID format"),
  tslsToUniReportSubmissionName: z.string(),
  tslsToUniReportSubmissionFilePath: z.string(),
  submittedAt: z.coerce.date(),
  notes: z.string(),
  submittedBy: z.string(),
  tslsToUniDropboxId: z.string().uuid("Invalid UUID format"),
});

export type TslsToUniReportSubmissionResponseDto = z.infer<typeof TslsToUniReportSubmissionResponseSchema>;

// TSLS to University Report Submission Create DTO Schema
export const TslsToUniReportSubmissionCreateSchema = z.object({
  tslsToUniReportSubmissionName: z.string().min(1, "Report submission name is required"),
  file: z.any(), // File or Buffer - will be handled by multipart form data
  notes: z.string().optional(),
  tslsToUniDropboxId: z.string().uuid("Invalid UUID format"),
});

export type TslsToUniReportSubmissionCreateDto = z.infer<typeof TslsToUniReportSubmissionCreateSchema>;

// ===== UNI TO TSLS DROPBOX DTOs =====

// University to TSLS Dropbox Response DTO Schema
export const UniToTslsDropboxResponseSchema = z.object({
  uniToTslsDropboxId: z.string().uuid("Invalid UUID format"),
  uniToTslsDropboxName: z.string(),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date().optional(),
  openingDate: z.coerce.date(),
  closingDate: z.coerce.date(),
  isOpen: z.boolean(),
  reportType: z.string().default('Standard'),
  universityId: z.string().uuid("Invalid UUID format"),
});

export type UniToTslsDropboxResponseDto = z.infer<typeof UniToTslsDropboxResponseSchema>;

// University to TSLS Dropbox Create DTO Schema
export const UniToTslsDropboxCreateSchema = z.object({
  uniToTslsDropboxName: z.string().min(1, "Dropbox name is required"),
  openingDate: z.coerce.date(),
  closingDate: z.coerce.date(),
  isOpen: z.boolean(),
  reportType: z.string().default('Standard'),
  universityId: z.string().uuid("Invalid UUID format"),
});

export type UniToTslsDropboxCreateDto = z.infer<typeof UniToTslsDropboxCreateSchema>;

// University to TSLS Dropbox Update DTO Schema
export const UniToTslsDropboxUpdateSchema = z.object({
  openingDate: z.coerce.date(),
  closingDate: z.coerce.date(),
  isOpen: z.boolean(),
});

export type UniToTslsDropboxUpdateDto = z.infer<typeof UniToTslsDropboxUpdateSchema>;

// ===== UNI TO TSLS REPORT SUBMISSION DTOs =====

// University to TSLS Report Submission Response DTO Schema
export const UniToTslsReportSubmissionResponseSchema = z.object({
  uniToTslsReportSubmissionId: z.string().uuid("Invalid UUID format"),
  uniToTslsReportSubmissionName: z.string(),
  uniToTslsReportSubmissionFilePath: z.string(),
  submittedAt: z.coerce.date(),
  notes: z.string(),
  submittedBy: z.string(),
  uniToTslsDropboxId: z.string().uuid("Invalid UUID format"),
});

export type UniToTslsReportSubmissionResponseDto = z.infer<typeof UniToTslsReportSubmissionResponseSchema>;

// University to TSLS Report Submission Create DTO Schema
export const UniToTslsReportSubmissionCreateSchema = z.object({
  uniToTslsReportSubmissionName: z.string().min(1, "Report submission name is required"),
  file: z.any(), // File or Buffer - will be handled by multipart form data
  notes: z.string().optional(),
  uniToTslsDropboxId: z.string().uuid("Invalid UUID format"),
});

export type UniToTslsReportSubmissionCreateDto = z.infer<typeof UniToTslsReportSubmissionCreateSchema>;

// Export schemas with DtoSchema suffix for backward compatibility
export const TslsToUniDropboxCreateDtoSchema = TslsToUniDropboxCreateSchema;
export const TslsToUniDropboxUpdateDtoSchema = TslsToUniDropboxUpdateSchema;
export const TslsToUniReportSubmissionCreateDtoSchema = TslsToUniReportSubmissionCreateSchema;
export const UniToTslsDropboxCreateDtoSchema = UniToTslsDropboxCreateSchema;
export const UniToTslsDropboxUpdateDtoSchema = UniToTslsDropboxUpdateSchema;
export const UniToTslsReportSubmissionCreateDtoSchema = UniToTslsReportSubmissionCreateSchema;

// Add missing DTO types that are being imported
export type TslsToUniDropboxDto = TslsToUniDropboxResponseDto;
export type UniToTslsDropboxDto = UniToTslsDropboxResponseDto;
export type UniToTslsReportSubmissionDto = UniToTslsReportSubmissionResponseDto;