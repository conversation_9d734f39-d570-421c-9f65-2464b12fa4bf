// Report Settings Service - Business logic for report settings operations
import { PrismaClient } from '../../generated/prisma/index.js';
import { ReportSettings } from '../../domain/ReportSettings.entity.js';
import { ReportSettingsRepository } from '../../infrastructure/repositories/ReportSettingsRepository.js';
import {
  ReportSettingsDto,
  CreateReportSettingsDto,
  UpdateReportSettingsDto,
  CreateReportSettingsSchema,
  UpdateReportSettingsSchema,
  ReportSettingsResponseDto
} from '../dto/reportSettings.dto.js';
import { DtoValidator } from '../validation/DtoValidator.js';
import crypto from 'crypto';

// Report Type to Dropbox Type mapping
const REPORT_TYPE_TO_DROPBOX_MAPPING = {
  'Enrolment Report': 'Enrolment',
  'Student Results Report': 'StudentResults',
  'Tagging Report': 'Tagging',
  'Graduation Report': 'Graduation',
  'Billing Report': 'Billing',
  'Change of Program Report': 'ChangeOfProgram'
} as const;

// Helper function to convert database entity to DTO
function toReportSettingsDto(dbReportSettings: any): ReportSettingsDto {
  return {
    reportSettingsId: dbReportSettings.ReportSettingsId,
    reportType: dbReportSettings.ReportType,
    reportOpeningDate: dbReportSettings.ReportOpeningDate,
    reportClosingDate: dbReportSettings.ReportClosingDate,
    isActive: dbReportSettings.IsActive,
    createdBy: dbReportSettings.CreatedBy,
    createdAt: dbReportSettings.CreatedAt,
    updatedAt: dbReportSettings.UpdatedAt
  };
}

export class ReportSettingsService {
  private repository: ReportSettingsRepository;

  constructor(private prisma: PrismaClient) {
    this.repository = new ReportSettingsRepository(prisma);
  }

  /**
   * Opens dropboxes based on report type
   * @param reportType - The report type to match with dropboxes
   * @param openingDate - When to open the dropboxes
   * @param closingDate - When to close the dropboxes
   */
  private async openDropboxesForReportType(reportType: string, openingDate: Date, closingDate: Date): Promise<void> {
    const dropboxType = REPORT_TYPE_TO_DROPBOX_MAPPING[reportType as keyof typeof REPORT_TYPE_TO_DROPBOX_MAPPING];

    if (!dropboxType) {
      console.warn(`No dropbox type mapping found for report type: ${reportType}`);
      return;
    }

    console.log(`🔓 Opening dropboxes for report type: ${reportType} -> dropbox type: ${dropboxType}`);

    // Update TslsToUniDropbox dropboxes
    await this.prisma.tslsToUniDropbox.updateMany({
      where: {
        TslsToUniDropboxName: {
          contains: dropboxType
        }
      },
      data: {
        IsOpenStatus: true,
        OpeningDate: openingDate,
        ClosingDate: closingDate,
        UpdatedAt: new Date()
      }
    });

    // Update UniToTslsDropbox dropboxes
    await this.prisma.uniToTslsDropbox.updateMany({
      where: {
        UniToTslsDropboxName: {
          contains: dropboxType
        }
      },
      data: {
        IsOpen: true,
        OpeningDate: openingDate,
        ClosingDate: closingDate,
        UpdatedAt: new Date()
      }
    });

    console.log(`✅ Dropboxes opened for ${dropboxType}`);
  }

  /**
   * Closes dropboxes based on report type
   * @param reportType - The report type to match with dropboxes
   */
  private async closeDropboxesForReportType(reportType: string): Promise<void> {
    const dropboxType = REPORT_TYPE_TO_DROPBOX_MAPPING[reportType as keyof typeof REPORT_TYPE_TO_DROPBOX_MAPPING];

    if (!dropboxType) {
      console.warn(`No dropbox type mapping found for report type: ${reportType}`);
      return;
    }

    console.log(`🔒 Closing dropboxes for report type: ${reportType} -> dropbox type: ${dropboxType}`);

    // Update TslsToUniDropbox dropboxes
    await this.prisma.tslsToUniDropbox.updateMany({
      where: {
        TslsToUniDropboxName: {
          contains: dropboxType
        }
      },
      data: {
        IsOpenStatus: false,
        UpdatedAt: new Date()
      }
    });

    // Update UniToTslsDropbox dropboxes
    await this.prisma.uniToTslsDropbox.updateMany({
      where: {
        UniToTslsDropboxName: {
          contains: dropboxType
        }
      },
      data: {
        IsOpen: false,
        UpdatedAt: new Date()
      }
    });

    console.log(`✅ Dropboxes closed for ${dropboxType}`);
  }

  async getAllReportSettingsAsync(): Promise<ReportSettingsDto[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async getAllActiveReportSettingsAsync(): Promise<ReportSettingsDto[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: { IsActive: true },
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async getReportSettingByIdAsync(id: string): Promise<ReportSettingsDto> {
    const prismaReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!prismaReportSetting) {
      throw new Error('Report setting not found');
    }

    return toReportSettingsDto(prismaReportSetting);
  }

  async getReportSettingByTypeAsync(reportType: string): Promise<ReportSettingsDto | null> {
    const prismaReportSetting = await this.prisma.reportSettings.findFirst({
      where: {
        ReportType: reportType,
        IsActive: true
      },
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSetting ? toReportSettingsDto(prismaReportSetting) : null;
  }

  /**
   * Gets all report settings for a specific report type
   * @param reportType - The report type to search for
   * @returns Array of report settings with the specified type
   */
  async getReportSettingsByTypeAsync(reportType: string): Promise<ReportSettingsDto[]> {
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        ReportType: reportType,
        IsActive: true
      },
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async createReportSettingAsync(createDto: CreateReportSettingsDto, createdBy: string): Promise<ReportSettingsDto> {
    // Validate DTO
    const validatedDto = DtoValidator.validate(createDto, CreateReportSettingsSchema);

    // Sanitize input
    const sanitizedDto = DtoValidator.sanitizeUserInput(validatedDto);

    // Note: Removed duplicate report type check to allow multiple reports with same type but different dates

    // Validate dates
    const openingDate = new Date(sanitizedDto.reportOpeningDate);
    const closingDate = new Date(sanitizedDto.reportClosingDate);

    if (closingDate < openingDate) {
      throw new Error('Closing date must be on or after opening date');
    }

    // Create domain entity
    const reportSetting = new ReportSettings(
      crypto.randomUUID(),
      sanitizedDto.reportType,
      openingDate,
      closingDate,
      true,
      createdBy,
      new Date(),
      new Date()
    );

    // Save to database
    const prismaData = {
      ReportSettingsId: reportSetting.reportSettingsId,
      ReportType: reportSetting.reportType,
      ReportOpeningDate: reportSetting.reportOpeningDate,
      ReportClosingDate: reportSetting.reportClosingDate,
      IsActive: reportSetting.isActive,
      CreatedBy: reportSetting.createdBy,
      CreatedAt: reportSetting.createdAt,
      UpdatedAt: reportSetting.updatedAt
    };

    const createdReportSetting = await this.prisma.reportSettings.create({
      data: prismaData
    });

    // Open corresponding dropboxes immediately if the report is currently active
    const now = new Date();
    if (openingDate <= now && closingDate >= now) {
      await this.openDropboxesForReportType(sanitizedDto.reportType, openingDate, closingDate);
    }

    return toReportSettingsDto(createdReportSetting);
  }

  async updateReportSettingAsync(id: string, updateDto: UpdateReportSettingsDto, updatedBy: string): Promise<ReportSettingsDto> {
    // Validate DTO
    const validatedDto = DtoValidator.validate(updateDto, UpdateReportSettingsSchema);

    // Sanitize input
    const sanitizedDto = DtoValidator.sanitizeUserInput(validatedDto);

    // Check if report setting exists
    const existingReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!existingReportSetting) {
      throw new Error('Report setting not found');
    }

    // Check if report type already exists (if being updated)
    if (sanitizedDto.reportType && sanitizedDto.reportType !== existingReportSetting.ReportType) {
      const duplicateReportSetting = await this.prisma.reportSettings.findFirst({
        where: { 
          ReportType: sanitizedDto.reportType,
          ReportSettingsId: { not: id }
        }
      });

      if (duplicateReportSetting) {
        throw new Error('Report setting with this type already exists');
      }
    }

    // Validate dates if both are provided
    if (sanitizedDto.reportOpeningDate && sanitizedDto.reportClosingDate) {
      const openingDate = new Date(sanitizedDto.reportOpeningDate);
      const closingDate = new Date(sanitizedDto.reportClosingDate);

      if (closingDate < openingDate) {
        throw new Error('Closing date must be on or after opening date');
      }
    }

    // Prepare update data
    const updateData: any = {
      UpdatedAt: new Date()
    };

    if (sanitizedDto.reportType) {
      updateData.ReportType = sanitizedDto.reportType;
    }
    if (sanitizedDto.reportOpeningDate) {
      updateData.ReportOpeningDate = new Date(sanitizedDto.reportOpeningDate);
    }
    if (sanitizedDto.reportClosingDate) {
      updateData.ReportClosingDate = new Date(sanitizedDto.reportClosingDate);
    }
    if (sanitizedDto.isActive !== undefined) {
      updateData.IsActive = sanitizedDto.isActive;
    }

    const updatedReportSetting = await this.prisma.reportSettings.update({
      where: { ReportSettingsId: id },
      data: updateData
    });

    return toReportSettingsDto(updatedReportSetting);
  }

  async deleteReportSettingAsync(id: string): Promise<void> {
    const existingReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!existingReportSetting) {
      throw new Error('Report setting not found');
    }

    await this.prisma.reportSettings.delete({
      where: { ReportSettingsId: id }
    });
  }

  async softDeleteReportSettingAsync(id: string): Promise<void> {
    const existingReportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: id }
    });

    if (!existingReportSetting) {
      throw new Error('Report setting not found');
    }

    await this.prisma.reportSettings.update({
      where: { ReportSettingsId: id },
      data: { 
        IsActive: false,
        UpdatedAt: new Date()
      }
    });
  }

  async getCurrentlyOpenReportSettingsAsync(): Promise<ReportSettingsDto[]> {
    const now = new Date();
    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        IsActive: true,
        ReportOpeningDate: { lte: now },
        ReportClosingDate: { gte: now }
      },
      orderBy: { CreatedAt: 'desc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  async getUpcomingReportSettingsAsync(daysAhead: number = 7): Promise<ReportSettingsDto[]> {
    const now = new Date();
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + daysAhead);

    const prismaReportSettings = await this.prisma.reportSettings.findMany({
      where: {
        IsActive: true,
        ReportOpeningDate: { 
          gte: now,
          lte: futureDate 
        }
      },
      orderBy: { ReportOpeningDate: 'asc' }
    });

    return prismaReportSettings.map(toReportSettingsDto);
  }

  /**
   * Manually opens dropboxes for a specific report setting
   * @param reportSettingsId - The report settings ID
   */
  async openDropboxesForReportSetting(reportSettingsId: string): Promise<void> {
    const reportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: reportSettingsId }
    });

    if (!reportSetting) {
      throw new Error('Report setting not found');
    }

    if (!reportSetting.IsActive) {
      throw new Error('Cannot open dropboxes for inactive report setting');
    }

    await this.openDropboxesForReportType(
      reportSetting.ReportType,
      reportSetting.ReportOpeningDate,
      reportSetting.ReportClosingDate
    );
  }

  /**
   * Manually closes dropboxes for a specific report setting
   * @param reportSettingsId - The report settings ID
   */
  async closeDropboxesForReportSetting(reportSettingsId: string): Promise<void> {
    const reportSetting = await this.prisma.reportSettings.findUnique({
      where: { ReportSettingsId: reportSettingsId }
    });

    if (!reportSetting) {
      throw new Error('Report setting not found');
    }

    await this.closeDropboxesForReportType(reportSetting.ReportType);
  }

  /**
   * Gets the status of dropboxes for a specific report type
   * @param reportType - The report type
   */
  async getDropboxStatusForReportType(reportType: string): Promise<{
    tslsToUniDropboxes: any[];
    uniToTslsDropboxes: any[];
  }> {
    const dropboxType = REPORT_TYPE_TO_DROPBOX_MAPPING[reportType as keyof typeof REPORT_TYPE_TO_DROPBOX_MAPPING];

    if (!dropboxType) {
      return {
        tslsToUniDropboxes: [],
        uniToTslsDropboxes: []
      };
    }

    const [tslsToUniDropboxes, uniToTslsDropboxes] = await Promise.all([
      this.prisma.tslsToUniDropbox.findMany({
        where: {
          TslsToUniDropboxName: {
            contains: dropboxType
          }
        },
        select: {
          TslsToUniDropboxId: true,
          TslsToUniDropboxName: true,
          IsOpenStatus: true,
          OpeningDate: true,
          ClosingDate: true,
          UpdatedAt: true
        }
      }),
      this.prisma.uniToTslsDropbox.findMany({
        where: {
          UniToTslsDropboxName: {
            contains: dropboxType
          }
        },
        select: {
          UniToTslsDropboxId: true,
          UniToTslsDropboxName: true,
          IsOpen: true,
          OpeningDate: true,
          ClosingDate: true,
          UpdatedAt: true
        }
      })
    ]);

    return {
      tslsToUniDropboxes,
      uniToTslsDropboxes
    };
  }

  /**
   * Processes all report settings to open/close dropboxes based on current time
   * This method should be called periodically (e.g., via cron job)
   */
  async processReportSettingsSchedule(): Promise<void> {
    console.log('🕐 Processing report settings schedule...');

    const now = new Date();
    const allActiveReportSettings = await this.prisma.reportSettings.findMany({
      where: { IsActive: true }
    });

    for (const reportSetting of allActiveReportSettings) {
      const isCurrentlyOpen = now >= reportSetting.ReportOpeningDate && now <= reportSetting.ReportClosingDate;
      const wasOpen = await this.isDropboxCurrentlyOpen(reportSetting.ReportType);

      if (isCurrentlyOpen && !wasOpen) {
        // Should be open but isn't - open it
        console.log(`📅 Opening dropboxes for ${reportSetting.ReportType} (schedule activated)`);
        await this.openDropboxesForReportType(
          reportSetting.ReportType,
          reportSetting.ReportOpeningDate,
          reportSetting.ReportClosingDate
        );
      } else if (!isCurrentlyOpen && wasOpen) {
        // Should be closed but isn't - close it
        console.log(`📅 Closing dropboxes for ${reportSetting.ReportType} (schedule expired)`);
        await this.closeDropboxesForReportType(reportSetting.ReportType);
      }
    }

    console.log('✅ Report settings schedule processing completed');
  }

  /**
   * Checks if dropboxes for a report type are currently open
   * @param reportType - The report type to check
   */
  private async isDropboxCurrentlyOpen(reportType: string): Promise<boolean> {
    const dropboxType = REPORT_TYPE_TO_DROPBOX_MAPPING[reportType as keyof typeof REPORT_TYPE_TO_DROPBOX_MAPPING];

    if (!dropboxType) {
      return false;
    }

    const openDropboxCount = await this.prisma.tslsToUniDropbox.count({
      where: {
        TslsToUniDropboxName: {
          contains: dropboxType
        },
        IsOpenStatus: true
      }
    });

    return openDropboxCount > 0;
  }
}
