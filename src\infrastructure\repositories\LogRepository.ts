// ---------- FILE PATH
// src/infrastructure/repositories/log.repository.ts

// ---------- DESCRIPTION
// This file contains the log repository for interacting with the log data source.

// Import PrismaClient
import { PrismaClient } from "../../generated/prisma/index.js";

// Import the required interfaces.
import { ILogRepository } from "../../application/interfaces/IRepositories/ILog.Repositories.js";

// Import the log entity.
import { Log, UserInfo } from "../../domain/Log.entity.js";

// Initialize PrismaClient
const prisma = new PrismaClient();

export class LogRepository implements ILogRepository {
  // Helper method to convert Prisma log to domain Log entity
  private mapPrismaLogToEntity(prismaLog: any): Log {
    let user: UserInfo | null = null;

    if (prismaLog.PTEIUser) {
      user = {
        userId: prismaLog.PTEIUser.UserId,
        firstName: prismaLog.PTEIUser.FirstName || "",
        lastName: prismaLog.PTEIUser.LastName || "",
        email: prismaLog.PTEIUser.email,
        userType: prismaLog.PTEIUser.UserType,
      };
    }

    // Ensure timestamp is properly converted to Date object
    let timestamp = prismaLog.timeStamp;
    if (typeof timestamp === 'string') {
      timestamp = new Date(timestamp);
    }

    // Debug logging for timestamp conversion
    console.log('🔄 Converting Prisma log to entity:', {
      id: prismaLog.id,
      originalTimestamp: prismaLog.timeStamp,
      convertedTimestamp: timestamp,
      timestampType: typeof prismaLog.timeStamp
    });

    const log = new Log(
      prismaLog.id,
      prismaLog.description,
      prismaLog.ipAddress,
      timestamp,
      prismaLog.deviceInfo ?? null,
      prismaLog.browserInfo ?? null,
      prismaLog.actionType,
      prismaLog.pTEIUserUserId ?? null,
      user
    );

    // Test the formatted timestamp method
    const formattedTimestamp = log.getFormattedTimestamp();
    console.log('✅ Log entity created with formatted timestamp:', formattedTimestamp);

    return log;
  }

  async create(log: Log): Promise<Log> {
    const created = await prisma.log.create({
      data: {
        id: log.id,
        description: log.description,
        ipAddress: log.ipAddress,
        timeStamp: log.timeStamp,
        deviceInfo: log.deviceInfo ?? undefined,
        browserInfo: log.browserInfo ?? undefined,
        actionType: log.actionType,
        pTEIUserUserId: log.pTEIUserUserId ?? undefined,
      },
      include: {
        PTEIUser: {
          select: {
            UserId: true,
            FirstName: true,
            LastName: true,
            email: true,
            UserType: true,
          },
        },
      },
    });

    return this.mapPrismaLogToEntity(created);
  }

  async findAll(): Promise<Log[]> {
    const logs = await prisma.log.findMany({
      include: {
        PTEIUser: {
          select: {
            UserId: true,
            FirstName: true,
            LastName: true,
            email: true,
            UserType: true,
          },
        },
      },
      orderBy: { timeStamp: 'desc' },
    });

    return logs.map(log => this.mapPrismaLogToEntity(log));
  }

  async findById(id: string): Promise<Log | null> {
    const log = await prisma.log.findUnique({
      where: { id },
      include: {
        PTEIUser: {
          select: {
            UserId: true,
            FirstName: true,
            LastName: true,
            email: true,
            UserType: true,
          },
        },
      },
    });

    if (!log) return null;
    return this.mapPrismaLogToEntity(log);
  }

  async findWithPagination(
    page: number,
    limit: number,
    filters?: {
      startDate?: Date;
      endDate?: Date;
      actionType?: string;
      userId?: string;
    }
  ): Promise<{ logs: Log[]; total: number }> {
    const skip = (page - 1) * limit;

    const where: any = {};

    if (filters?.startDate || filters?.endDate) {
      where.timeStamp = {};
      if (filters.startDate) where.timeStamp.gte = filters.startDate;
      if (filters.endDate) where.timeStamp.lte = filters.endDate;
    }

    if (filters?.actionType) {
      where.actionType = filters.actionType;
    }

    if (filters?.userId) {
      where.pTEIUserUserId = filters.userId;
    }

    const [logs, total] = await Promise.all([
      prisma.log.findMany({
        where,
        skip,
        take: limit,
        orderBy: { timeStamp: 'desc' },
        include: {
          PTEIUser: {
            select: {
              UserId: true,
              FirstName: true,
              LastName: true,
              email: true,
              UserType: true,
            },
          },
        },
      }),
      prisma.log.count({ where }),
    ]);

    return {
      logs: logs.map(log => this.mapPrismaLogToEntity(log)),
      total,
    };
  }

  async findByUserId(userId: string, page: number = 1, limit: number = 10): Promise<{ logs: Log[]; total: number }> {
    return this.findWithPagination(page, limit, { userId });
  }

  async findByActionType(actionType: string, page: number = 1, limit: number = 10): Promise<{ logs: Log[]; total: number }> {
    return this.findWithPagination(page, limit, { actionType });
  }

  async getLogStats(): Promise<{
    totalLogs: number;
    logsToday: number;
    logsThisWeek: number;
    logsThisMonth: number;
    topActions: Array<{ actionType: string; count: number }>;
    recentActivity: Log[];
  }> {
    const now = new Date();
    const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const startOfWeek = new Date(startOfDay);
    startOfWeek.setDate(startOfDay.getDate() - startOfDay.getDay());
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);

    const [
      totalLogs,
      logsToday,
      logsThisWeek,
      logsThisMonth,
      topActionsRaw,
      recentActivityRaw,
    ] = await Promise.all([
      prisma.log.count(),
      prisma.log.count({ where: { timeStamp: { gte: startOfDay } } }),
      prisma.log.count({ where: { timeStamp: { gte: startOfWeek } } }),
      prisma.log.count({ where: { timeStamp: { gte: startOfMonth } } }),
      prisma.log.groupBy({
        by: ['actionType'],
        _count: { actionType: true },
        orderBy: { _count: { actionType: 'desc' } },
        take: 5,
      }),
      prisma.log.findMany({
        take: 10,
        orderBy: { timeStamp: 'desc' },
        include: {
          PTEIUser: {
            select: {
              UserId: true,
              FirstName: true,
              LastName: true,
              email: true,
              UserType: true,
            },
          },
        },
      }),
    ]);

    const topActions = topActionsRaw.map((item) => ({
      actionType: item.actionType,
      count: item._count.actionType,
    }));

    const recentActivity = recentActivityRaw.map(log => this.mapPrismaLogToEntity(log));

    return {
      totalLogs,
      logsToday,
      logsThisWeek,
      logsThisMonth,
      topActions,
      recentActivity,
    };
  }
}